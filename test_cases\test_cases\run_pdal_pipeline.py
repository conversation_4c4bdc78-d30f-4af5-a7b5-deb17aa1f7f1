#!/usr/bin/env python3
"""
PDAL Pipeline Runner
Executes the PDAL pipeline to filter and reproject LiDAR data
"""

import subprocess
import os
import sys
import json
from pathlib import Path

def run_pdal_pipeline(pipeline_file, verbose=True):
    """
    Run PDAL pipeline from JSON configuration file
    
    Args:
        pipeline_file (str): Path to PDAL pipeline JSON file
        verbose (bool): Print verbose output
    
    Returns:
        bool: True if successful, False otherwise
    """
    
    if not os.path.exists(pipeline_file):
        print(f"❌ Pipeline file not found: {pipeline_file}")
        return False
    
    try:
        # Load and validate pipeline
        with open(pipeline_file, 'r') as f:
            pipeline = json.load(f)
        
        if verbose:
            print(f"🔧 Running PDAL pipeline: {pipeline_file}")
            print(f"📂 Input file: {pipeline['pipeline'][0]['filename']}")
            print(f"📂 Output file: {pipeline['pipeline'][-1]['filename']}")
        
        # Check if input file exists
        input_file = pipeline['pipeline'][0]['filename']
        if not os.path.exists(input_file):
            print(f"❌ Input LAS file not found: {input_file}")
            return False
        
        # Create output directory if it doesn't exist
        output_file = pipeline['pipeline'][-1]['filename']
        output_dir = os.path.dirname(output_file)
        os.makedirs(output_dir, exist_ok=True)
        
        # Run PDAL command
        cmd = ['pdal', 'pipeline', pipeline_file]
        
        if verbose:
            print(f"🚀 Executing: {' '.join(cmd)}")
        
        result = subprocess.run(
            cmd,
            capture_output=True,
            text=True,
            check=True
        )
        
        if verbose:
            print("✅ PDAL pipeline completed successfully!")
            if result.stdout:
                print("STDOUT:", result.stdout)
        
        # Verify output file was created
        if os.path.exists(output_file):
            file_size = os.path.getsize(output_file) / (1024 * 1024)  # MB
            print(f"📁 Output file created: {output_file} ({file_size:.2f} MB)")
            return True
        else:
            print(f"❌ Output file was not created: {output_file}")
            return False
            
    except subprocess.CalledProcessError as e:
        print(f"❌ PDAL pipeline failed with return code {e.returncode}")
        print(f"STDERR: {e.stderr}")
        return False
    except Exception as e:
        print(f"❌ Error running PDAL pipeline: {str(e)}")
        return False

def main():
    """Main function to run the pipeline"""
    
    # Get the directory of this script
    script_dir = Path(__file__).parent
    pipeline_file = script_dir / "pdal_pipeline.json"
    
    print("🎯 PDAL LiDAR Processing Pipeline")
    print("=" * 50)
    
    success = run_pdal_pipeline(str(pipeline_file))
    
    if success:
        print("\n🎉 Pipeline completed successfully!")
        print("Next steps:")
        print("  1. Run chunk_processor.py to process data in chunks")
        print("  2. Run geojson_generator.py to extract polygons and lines")
    else:
        print("\n💥 Pipeline failed!")
        sys.exit(1)

if __name__ == "__main__":
    main()
