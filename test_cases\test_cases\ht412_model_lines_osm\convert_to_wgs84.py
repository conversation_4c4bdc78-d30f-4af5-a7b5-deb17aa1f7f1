#!/usr/bin/env python3
"""
Convert enhanced lanes with OSM data to WGS84 for web compatibility
"""

import geopandas as gpd
from pathlib import Path

def convert_to_wgs84():
    """Convert the enhanced lanes with OSM data to WGS84"""
    
    print("=" * 60)
    print("CONVERTING ENHANCED LANES TO WGS84")
    print("=" * 60)
    
    # Input files
    input_files = [
        "ht412_enhanced_lanes_with_osm.geojson",
        "ht412_enhanced_lanes_connected_lines.geojson", 
        "ht412_enhanced_lanes_osm_data.geojson"
    ]
    
    for input_file in input_files:
        if not Path(input_file).exists():
            print(f"⚠️  File not found: {input_file}")
            continue
            
        print(f"\n📁 Processing: {input_file}")
        
        try:
            # Load the GeoDataFrame
            gdf = gpd.read_file(input_file)
            
            print(f"   Current CRS: {gdf.crs}")
            print(f"   Features: {len(gdf)}")
            
            # Convert to WGS84
            gdf_wgs84 = gdf.to_crs("EPSG:4326")
            
            # Create output filename
            output_file = input_file.replace(".geojson", "_wgs84.geojson")
            
            # Save as WGS84
            gdf_wgs84.to_file(output_file, driver="GeoJSON")
            
            print(f"   ✅ Converted to WGS84: {output_file}")
            
            # Show sample coordinates
            if len(gdf_wgs84) > 0:
                first_geom = gdf_wgs84.iloc[0].geometry
                if hasattr(first_geom, 'coords'):
                    coords = list(first_geom.coords)
                    if coords:
                        lon, lat = coords[0]
                        print(f"   📍 Sample coordinates: {lon:.6f}, {lat:.6f}")
                        
        except Exception as e:
            print(f"   ❌ Error processing {input_file}: {e}")
    
    print(f"\n🎯 WGS84 CONVERSION COMPLETED!")
    print(f"\nGenerated WGS84 files:")
    
    wgs84_files = [
        "ht412_enhanced_lanes_with_osm_wgs84.geojson",
        "ht412_enhanced_lanes_connected_lines_wgs84.geojson",
        "ht412_enhanced_lanes_osm_data_wgs84.geojson"
    ]
    
    for wgs84_file in wgs84_files:
        if Path(wgs84_file).exists():
            file_size = Path(wgs84_file).stat().st_size / 1024  # KB
            print(f"  ✅ {wgs84_file} ({file_size:.1f} KB)")
        else:
            print(f"  ❌ {wgs84_file} (not created)")
    
    print(f"\n📋 USAGE RECOMMENDATIONS:")
    print(f"  🌐 For web mapping/geojson.io: Use the _wgs84.geojson files")
    print(f"  🗺️  For GIS analysis: Use the original UTM32N files")
    print(f"  📊 Main file for visualization: ht412_enhanced_lanes_with_osm_wgs84.geojson")
    
    # Check if main WGS84 file was created successfully
    main_wgs84_file = "ht412_enhanced_lanes_with_osm_wgs84.geojson"
    if Path(main_wgs84_file).exists():
        print(f"\n🎉 SUCCESS! You can now use {main_wgs84_file} with:")
        print(f"  • geojson.io for web visualization")
        print(f"  • Leaflet/OpenLayers web maps")
        print(f"  • Any application expecting WGS84 coordinates")
        
        # Show coordinate range
        try:
            gdf_check = gpd.read_file(main_wgs84_file)
            bounds = gdf_check.total_bounds
            print(f"\n📍 WGS84 Coordinate bounds:")
            print(f"  West: {bounds[0]:.6f}°")
            print(f"  South: {bounds[1]:.6f}°") 
            print(f"  East: {bounds[2]:.6f}°")
            print(f"  North: {bounds[3]:.6f}°")
            
        except Exception as e:
            print(f"  Could not read bounds: {e}")

if __name__ == "__main__":
    convert_to_wgs84()
