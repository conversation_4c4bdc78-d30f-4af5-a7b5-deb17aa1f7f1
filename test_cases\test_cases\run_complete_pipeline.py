#!/usr/bin/env python3
"""
Complete LiDAR Processing Pipeline
Runs the entire pipeline from PDAL filtering to GeoJSON generation
"""

import os
import sys
import time
from pathlib import Path
import subprocess

def run_script(script_path, description):
    """Run a Python script and handle errors"""
    print(f"\n{'='*60}")
    print(f"🚀 {description}")
    print(f"{'='*60}")
    
    try:
        result = subprocess.run([
            sys.executable, str(script_path)
        ], check=True, capture_output=True, text=True)
        
        print(result.stdout)
        if result.stderr:
            print("STDERR:", result.stderr)
        
        print(f"✅ {description} completed successfully!")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} failed with return code {e.returncode}")
        print("STDOUT:", e.stdout)
        print("STDERR:", e.stderr)
        return False
    except Exception as e:
        print(f"❌ Error running {description}: {str(e)}")
        return False

def check_dependencies():
    """Check if required dependencies are available"""
    print("🔍 Checking dependencies...")
    
    required_packages = [
        'pdal', 'laspy', 'numpy', 'pandas', 'geopandas', 
        'shapely', 'sklearn', 'matplotlib'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            if package == 'pdal':
                # Check if PDAL command is available
                result = subprocess.run(['pdal', '--version'], 
                                      capture_output=True, text=True)
                if result.returncode != 0:
                    missing_packages.append('pdal (command line tool)')
            else:
                __import__(package)
        except (ImportError, FileNotFoundError):
            missing_packages.append(package)
    
    if missing_packages:
        print(f"❌ Missing dependencies: {', '.join(missing_packages)}")
        print("\nTo install missing Python packages:")
        python_packages = [p for p in missing_packages if not p.startswith('pdal')]
        if python_packages:
            print(f"pip install {' '.join(python_packages)}")
        
        if any('pdal' in p for p in missing_packages):
            print("\nTo install PDAL:")
            print("conda install -c conda-forge pdal")
            print("or visit: https://pdal.io/en/latest/download.html")
        
        return False
    
    print("✅ All dependencies are available!")
    return True

def check_input_file():
    """Check if input LAS file exists"""
    input_file = Path("D:/review_sample/FusedLidar_Lidar1.las")
    
    if not input_file.exists():
        print(f"❌ Input LAS file not found: {input_file}")
        print("Please ensure the file exists at the specified location.")
        return False
    
    file_size = input_file.stat().st_size / (1024 * 1024)  # MB
    print(f"✅ Input file found: {input_file} ({file_size:.2f} MB)")
    return True

def main():
    """Main pipeline execution"""
    start_time = time.time()
    
    print("🎯 Complete LiDAR Processing Pipeline")
    print("=" * 60)
    print("This pipeline will:")
    print("1. Filter LiDAR data using PDAL (intensity 75-150)")
    print("2. Reproject from EPSG:4326 to EPSG:25832 (UTM32N)")
    print("3. Process data in spatial chunks")
    print("4. Extract polygons and line strings")
    print("5. Generate GeoJSON output")
    print("=" * 60)
    
    # Get script directory
    script_dir = Path(__file__).parent
    
    # Check dependencies
    if not check_dependencies():
        print("\n💥 Pipeline aborted due to missing dependencies!")
        sys.exit(1)
    
    # Check input file
    if not check_input_file():
        print("\n💥 Pipeline aborted due to missing input file!")
        sys.exit(1)
    
    # Pipeline steps
    steps = [
        (script_dir / "run_pdal_pipeline.py", "PDAL Filtering and Reprojection"),
        (script_dir / "chunk_processor.py", "Chunk Processing"),
        (script_dir / "geojson_generator.py", "GeoJSON Generation")
    ]
    
    # Execute pipeline steps
    for step_script, step_description in steps:
        if not step_script.exists():
            print(f"❌ Script not found: {step_script}")
            sys.exit(1)
        
        success = run_script(step_script, step_description)
        
        if not success:
            print(f"\n💥 Pipeline failed at step: {step_description}")
            sys.exit(1)
        
        # Small delay between steps
        time.sleep(1)
    
    # Pipeline completed
    end_time = time.time()
    duration = end_time - start_time
    
    print(f"\n{'='*60}")
    print("🎉 COMPLETE PIPELINE FINISHED SUCCESSFULLY!")
    print(f"{'='*60}")
    print(f"⏱️  Total execution time: {duration:.2f} seconds")
    print(f"📁 Output files are in: {script_dir}")
    print("\nGenerated outputs:")
    print("  • FusedLidar_Lidar1_UTM32N_filtered.las - Filtered LAS file")
    print("  • chunks/ - Processed chunk files")
    print("  • geojson_output/ - GeoJSON feature files")
    print("    - polygons.geojson - Extracted polygons")
    print("    - lines.geojson - Extracted line strings")
    print("    - points.geojson - Sample points")
    print("    - combined_features.geojson - All features combined")
    
    # Check output files
    output_dir = script_dir / "geojson_output"
    if output_dir.exists():
        geojson_files = list(output_dir.glob("*.geojson"))
        print(f"\n📊 Generated {len(geojson_files)} GeoJSON files:")
        for file in geojson_files:
            file_size = file.stat().st_size / 1024  # KB
            print(f"   • {file.name} ({file_size:.1f} KB)")

if __name__ == "__main__":
    main()
