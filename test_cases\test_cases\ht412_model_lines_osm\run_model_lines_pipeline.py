#!/usr/bin/env python3
"""
HT412 Enhanced Model Line Strings with OSM Attributes Pipeline
Extracts line strings from HT412 LiDAR using models and enriches with OSM attributes
Now includes enhanced lane extraction with dashed lane connection
"""

import sys
import time
import subprocess
from pathlib import Path
import json
import argparse

def run_script(script_path, description, args=None):
    """Run a Python script and handle errors"""
    print(f"\n{'='*60}")
    print(f"[RUNNING] {description}")
    print(f"{'='*60}")

    try:
        cmd = [sys.executable, str(script_path)]
        if args:
            cmd.extend(args)

        result = subprocess.run(cmd, capture_output=True, text=True, check=True)

        print(result.stdout)
        if result.stderr:
            print("STDERR:", result.stderr)

        print(f"[SUCCESS] {description} completed successfully!")
        return True

    except subprocess.CalledProcessError as e:
        print(f"[ERROR] {description} failed with return code {e.returncode}")
        print("STDOUT:", e.stdout)
        print("STDERR:", e.stderr)
        return False
    except Exception as e:
        print(f"[ERROR] Error running {description}: {str(e)}")
        return False

def run_enhanced_lane_extraction(script_dir, enable_enhanced=True, intensity_thresh=22.0):
    """Run the enhanced lane extraction with dashed lane connection"""
    if not enable_enhanced:
        return True

    print(f"\n{'='*60}")
    print("[RUNNING] Enhanced Lane Extraction with Dashed Lane Connection")
    print(f"{'='*60}")

    # Path to the enhanced lane extraction script
    enhanced_script = script_dir.parent / "connected_lines_output/las_to_connected_lanes.py"

    if not enhanced_script.exists():
        print(f"[WARNING] Enhanced lane extraction script not found: {enhanced_script}")
        print("[INFO] Skipping enhanced lane extraction step")
        return True

    # Build command for enhanced extraction
    las_file = "D:/Integrate/HT412_1738935654_3217135_1422974157258029_1422974185645127_Clip.las"

    cmd = [
        sys.executable, str(enhanced_script),
        "-i", las_file,
        "-o", "ht412_enhanced_lanes",
        "--connect-dashed",
        "--integrate-osm",
        "--intensity-thresh", str(intensity_thresh),
        "--dashed-gap", "20.0",
        "--dashed-angle", "25.0",
        "--target-crs", "EPSG:25832"  # Match existing pipeline CRS
    ]

    try:
        print("Running enhanced lane extraction...")
        print(" ".join(cmd))
        print()

        result = subprocess.run(cmd, capture_output=True, text=True, check=True, cwd=str(script_dir))

        print(result.stdout)
        if result.stderr:
            print("STDERR:", result.stderr)

        print(f"[SUCCESS] Enhanced lane extraction completed successfully!")
        return True

    except subprocess.CalledProcessError as e:
        print(f"[ERROR] Enhanced lane extraction failed with return code {e.returncode}")
        print("STDOUT:", e.stdout)
        print("STDERR:", e.stderr)
        print("[WARNING] Continuing with standard pipeline...")
        return False
    except Exception as e:
        print(f"[ERROR] Error running enhanced lane extraction: {str(e)}")
        print("[WARNING] Continuing with standard pipeline...")
        return False

def check_dependencies():
    """Check if required dependencies are available"""
    print("[INFO] Checking dependencies...")

    # Core required packages (including enhanced lane extraction dependencies)
    required_packages = [
        'laspy', 'numpy', 'pandas', 'geopandas',
        'shapely', 'sklearn', 'matplotlib', 'pyproj', 'overpy',
        'requests', 'scipy'  # Additional for enhanced lane extraction
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
        except (ImportError, FileNotFoundError):
            missing_packages.append(package)
    
    if missing_packages:
        print(f"[ERROR] Missing dependencies: {', '.join(missing_packages)}")
        print("\nTo install missing Python packages:")
        python_packages = [p for p in missing_packages if not p.startswith('pdal')]
        if python_packages:
            print(f"conda run -n inti pip install {' '.join(python_packages)}")
        
        return False
    
    print("[SUCCESS] All dependencies are available!")
    return True

def check_input_file():
    """Check if input LAS file exists"""
    input_file = Path("D:/Integrate/HT412_1738935654_3217135_1422974157258029_1422974185645127_Clip.las")
    
    if not input_file.exists():
        print(f"[ERROR] Input LAS file not found: {input_file}")
        print("Please ensure the file exists at the specified location.")
        return False
    
    file_size = input_file.stat().st_size / (1024 * 1024)  # MB
    print(f"[SUCCESS] Input file found: {input_file.name} ({file_size:.2f} MB)")
    return True

def main():
    """Main pipeline execution"""
    parser = argparse.ArgumentParser(
        description="HT412 Enhanced Model Line Strings with OSM Attributes Pipeline",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Standard pipeline
  python run_model_lines_pipeline.py

  # With enhanced lane extraction
  python run_model_lines_pipeline.py --enhanced-lanes

  # Enhanced with custom intensity threshold
  python run_model_lines_pipeline.py --enhanced-lanes --intensity-thresh 20
        """
    )

    parser.add_argument('--enhanced-lanes', action='store_true',
                       help='Enable enhanced lane extraction with dashed lane connection')
    parser.add_argument('--intensity-thresh', type=float, default=22.0,
                       help='Intensity threshold for enhanced lane extraction (default: 22.0)')
    parser.add_argument('--skip-standard', action='store_true',
                       help='Skip standard pipeline and run only enhanced lane extraction')

    args = parser.parse_args()

    start_time = time.time()

    print("HT412 Enhanced Model Line Strings with OSM Attributes Pipeline")
    print("=" * 60)
    print("This pipeline will:")
    print("1. Verify intensity range 20-45 in HT412 LiDAR data")
    print("2. Extract line strings from LiDAR using ML models")
    print("3. Query OpenStreetMap for road/highway data")
    print("4. Associate OSM attributes with model-generated lines")
    print("5. Transform coordinates to EPSG:25832")
    print("6. Generate enriched GeoJSON output")
    print("7. Create WGS84 version for geojson.io")
    if args.enhanced_lanes:
        print("8. [ENHANCED] Extract lanes with dashed lane connection")
        print("9. [ENHANCED] Integrate with OSM data using advanced algorithms")
    print("=" * 60)
    
    # Get script directory
    script_dir = Path(__file__).parent
    
    # Check dependencies
    if not check_dependencies():
        print("\n[FAILED] Pipeline aborted due to missing dependencies!")
        sys.exit(1)
    
    # Check input file
    if not check_input_file():
        print("\n[FAILED] Pipeline aborted due to missing input file!")
        sys.exit(1)
    
    # Standard pipeline steps
    if not args.skip_standard:
        steps = [
            (script_dir / "check_intensity.py", "Intensity Range Verification"),
            (script_dir / "model_line_extractor.py", "Model Line String Extraction"),
            (script_dir / "osm_attribute_enricher.py", "OSM Attribute Enrichment"),
            (script_dir / "geojson_generator.py", "Final GeoJSON Generation"),
            (script_dir / "transform_to_wgs84.py", "WGS84 Transformation")
        ]

        # Execute standard pipeline steps
        for step_script, step_description in steps:
            if not step_script.exists():
                print(f"[ERROR] Script not found: {step_script}")
                sys.exit(1)

            success = run_script(step_script, step_description)

            if not success:
                print(f"\n[FAILED] Pipeline failed at step: {step_description}")
                sys.exit(1)

            # Small delay between steps
            time.sleep(1)
    else:
        print("[INFO] Skipping standard pipeline steps (--skip-standard enabled)")

    # Enhanced lane extraction step
    if args.enhanced_lanes:
        success = run_enhanced_lane_extraction(script_dir, True, args.intensity_thresh)
        if not success:
            print("[WARNING] Enhanced lane extraction failed, but continuing...")
        time.sleep(1)
    
    # Pipeline completed
    end_time = time.time()
    duration = end_time - start_time
    
    print(f"\n{'='*60}")
    print("[SUCCESS] HT412 ENHANCED MODEL LINES WITH OSM ATTRIBUTES PIPELINE COMPLETED!")
    print(f"{'='*60}")
    print(f"[INFO] Total execution time: {duration:.2f} seconds")
    print(f"[INFO] Output files are in: {script_dir}")
    print("\nGenerated outputs:")
    if not args.skip_standard:
        print("  STANDARD PIPELINE:")
        print("    - model_lines_with_osm_final_wgs84.geojson - Main output for geojson.io")
        print("    - model_lines_with_osm_final.geojson - Main output (UTM32N)")
        print("    - model_lines_only.geojson - Model-generated lines without OSM")
        print("    - osm_raw_data.geojson - Raw OSM data for reference")
        print("    - processing_statistics.json - Detailed statistics")
        print("    - processing_summary.txt - Human-readable summary")

    if args.enhanced_lanes:
        print("  ENHANCED LANE EXTRACTION:")
        print("    - ht412_enhanced_lanes_connected_lines.geojson - Connected lanes with dashed support")
        print("    - ht412_enhanced_lanes_points.geojson - Filtered LiDAR points")
        print("    - ht412_enhanced_lanes_comparison.png - Before/after visualization")
        print("    - ht412_enhanced_lanes_osm_data.geojson - OSM road data")
    
    # Check output files
    standard_files = [
        "model_lines_with_osm_final_wgs84.geojson",
        "model_lines_with_osm_final.geojson",
        "model_lines_only.geojson",
        "osm_raw_data.geojson"
    ]

    enhanced_files = [
        "ht412_enhanced_lanes_connected_lines.geojson",
        "ht412_enhanced_lanes_points.geojson",
        "ht412_enhanced_lanes_comparison.png",
        "ht412_enhanced_lanes_osm_data.geojson"
    ]

    print(f"\n[INFO] Generated files:")

    if not args.skip_standard:
        print("  STANDARD PIPELINE:")
        for filename in standard_files:
            file_path = script_dir / filename
            if file_path.exists():
                file_size = file_path.stat().st_size / 1024  # KB
                print(f"     ✓ {filename} ({file_size:.1f} KB)")
            else:
                print(f"     ✗ {filename} (NOT FOUND)")

    if args.enhanced_lanes:
        print("  ENHANCED LANE EXTRACTION:")
        for filename in enhanced_files:
            file_path = script_dir / filename
            if file_path.exists():
                file_size = file_path.stat().st_size / 1024  # KB
                print(f"     ✓ {filename} ({file_size:.1f} KB)")
            else:
                print(f"     ✗ {filename} (NOT FOUND)")

    # Summary recommendations
    print(f"\n[RECOMMENDATIONS]:")
    if args.enhanced_lanes:
        print("  1. Compare enhanced vs standard results:")
        print("     - Enhanced: ht412_enhanced_lanes_connected_lines.geojson")
        if not args.skip_standard:
            print("     - Standard: model_lines_with_osm_final.geojson")
        print("  2. View the comparison visualization: ht412_enhanced_lanes_comparison.png")
        print("  3. Load both outputs in QGIS to compare lane detection quality")
    else:
        print("  1. Try running with --enhanced-lanes for improved lane detection")
        print("  2. Enhanced extraction includes dashed lane connection and better OSM integration")

if __name__ == "__main__":
    main()
