#!/usr/bin/env python3
import argparse
import laspy
import pandas as pd
import numpy as np
from sklearn.cluster import DBSCAN
from sklearn.decomposition import PCA
from sklearn.linear_model import RANSACRegressor, LinearRegression
from sklearn.preprocessing import PolynomialFeatures
from sklearn.pipeline import make_pipeline
from shapely.geometry import MultiPoint, LineString, Point
from shapely.ops import linemerge, unary_union
import geopandas as gpd
import matplotlib.pyplot as plt
from scipy.spatial.distance import cdist

def connect_nearby_lines(lines, max_gap=5.0, angle_threshold=30.0, lateral_threshold=3.0):
    """Connect nearby line segments that are likely part of the same lane"""
    print(f"[INFO] Connecting nearby lines (max_gap: {max_gap}m, angle_threshold: {angle_threshold}°, lateral_threshold: {lateral_threshold}m)")

    if len(lines) < 2:
        return lines

    connected_lines = []
    used_indices = set()

    for i, line1 in enumerate(lines):
        if i in used_indices:
            continue

        # Start a new connected line group
        line_group = [line1]
        used_indices.add(i)

        # Find all lines that should be connected to this group
        changed = True
        while changed:
            changed = False

            for j, line2 in enumerate(lines):
                if j in used_indices:
                    continue

                # Check if line2 should be connected to any line in the group
                for group_line in line_group:
                    if should_connect_lines(group_line.geometry, line2.geometry, max_gap, angle_threshold, lateral_threshold):
                        line_group.append(line2)
                        used_indices.add(j)
                        changed = True
                        break

        # Merge the line group into a single line
        if len(line_group) > 1:
            merged_line = merge_line_group(line_group)
            if merged_line:
                connected_lines.append(merged_line)
        else:
            connected_lines.append(line_group[0])

    print(f"[INFO] Connected {len(lines)} fragments into {len(connected_lines)} continuous lines")
    return connected_lines

def should_connect_lines(line1, line2, max_gap, angle_threshold, lateral_threshold):
    """Check if two lines should be connected based on proximity, angle, and lateral distance"""
    # Get endpoints
    line1_start, line1_end = Point(line1.coords[0]), Point(line1.coords[-1])
    line2_start, line2_end = Point(line2.coords[0]), Point(line2.coords[-1])

    # Calculate all possible endpoint distances
    distances = [
        line1_start.distance(line2_start),
        line1_start.distance(line2_end),
        line1_end.distance(line2_start),
        line1_end.distance(line2_end)
    ]

    min_distance = min(distances)

    # Check distance threshold
    if min_distance > max_gap:
        return False

    # Check angle similarity
    angle1 = get_line_angle(line1)
    angle2 = get_line_angle(line2)
    angle_diff = abs(angle1 - angle2)
    angle_diff = min(angle_diff, 180 - angle_diff)  # Handle wraparound

    if angle_diff > angle_threshold:
        return False

    # Check lateral distance (perpendicular distance between line centers)
    # Get line centers
    center1 = Point((line1.coords[0][0] + line1.coords[-1][0])/2,
                    (line1.coords[0][1] + line1.coords[-1][1])/2)
    center2 = Point((line2.coords[0][0] + line2.coords[-1][0])/2,
                    (line2.coords[0][1] + line2.coords[-1][1])/2)

    # Calculate perpendicular distance
    # Use the average angle for perpendicular calculation
    avg_angle = (angle1 + angle2) / 2
    perp_angle = avg_angle + 90

    # Vector between centers
    dx = center2.x - center1.x
    dy = center2.y - center1.y

    # Project onto perpendicular direction
    perp_rad = np.radians(perp_angle)
    perp_x = np.cos(perp_rad)
    perp_y = np.sin(perp_rad)

    lateral_dist = abs(dx * perp_x + dy * perp_y)

    return lateral_dist <= lateral_threshold

def get_line_angle(line):
    """Get the angle of a line in degrees"""
    coords = list(line.coords)
    if len(coords) < 2:
        return 0
    
    start, end = coords[0], coords[-1]
    dx = end[0] - start[0]
    dy = end[1] - start[1]
    
    angle = np.degrees(np.arctan2(dy, dx))
    return angle % 180  # Normalize to 0-180

def merge_line_group(line_group):
    """Merge a group of lines into a single continuous line"""
    try:
        # Extract all coordinates from all lines
        all_coords = []
        for line_data in line_group:
            coords = list(line_data.geometry.coords)
            all_coords.extend(coords)
        
        if len(all_coords) < 2:
            return None
        
        # Remove duplicate points
        unique_coords = []
        for coord in all_coords:
            if not unique_coords or Point(coord).distance(Point(unique_coords[-1])) > 0.1:
                unique_coords.append(coord)
        
        if len(unique_coords) < 2:
            return None
        
        # Fit a line through all points using PCA
        coords_array = np.array(unique_coords)
        pca = PCA(n_components=2).fit(coords_array)
        center = pca.mean_
        direction = pca.components_[0]
        
        # Project all points onto the principal axis
        projections = []
        for coord in unique_coords:
            point_vec = np.array(coord) - center
            projection = np.dot(point_vec, direction)
            projections.append(projection)
        
        # Find the extent of projections
        min_proj, max_proj = min(projections), max(projections)
        
        # Create the merged line
        start_point = center + direction * min_proj
        end_point = center + direction * max_proj
        
        merged_line = LineString([start_point, end_point])
        
        # Combine properties from the first line
        properties = line_group[0].properties.copy()
        properties['merged_from'] = len(line_group)
        properties['length'] = merged_line.length
        
        return type('LineData', (), {
            'geometry': merged_line,
            'properties': properties
        })()
        
    except Exception as e:
        print(f"[WARNING] Failed to merge line group: {e}")
        return line_group[0]  # Return first line as fallback

def main():
    p = argparse.ArgumentParser(
        description="LAS → Connected centerlines with line merging for continuous lanes"
    )
    p.add_argument('-i','--input', required=True, help="Input LAS file")
    p.add_argument('-t','--intensity-thresh', type=float, default=30.0,
                   help="Min intensity to keep a point")
    p.add_argument('--ground-threshold', type=float, default=0.05,
                   help="Max Z residual (m) to classify as ground after RANSAC")
    p.add_argument('--eps', type=float, default=0.8,
                   help="DBSCAN eps (metres)")
    p.add_argument('--min-samples', type=int, default=15,
                   help="DBSCAN min_samples")
    p.add_argument('--max-gap', type=float, default=3.0,
                   help="Maximum gap to connect lines (metres)")
    p.add_argument('--angle-threshold', type=float, default=15.0,
                   help="Maximum angle difference to connect lines (degrees)")
    p.add_argument('--lateral-threshold', type=float, default=2.0,
                   help="Maximum lateral distance to connect lines (metres)")
    p.add_argument('-o','--output-prefix', default='connected_lanes',
                   help="Prefix for all output files")
    p.add_argument('--target-crs', default="EPSG:4326",
                   help="Output CRS (e.g. WGS84 lat/lon)")
    args = p.parse_args()

    # 1) Read LAS → xyz + intensity
    las   = laspy.read(args.input)
    xyz   = las.xyz                      # (N×3)
    inten = np.array(las.intensity, float)

    # 2) Filter by intensity
    df = pd.DataFrame(xyz, columns=['X','Y','Z'])
    df['Intensity'] = inten
    df = df[df['Intensity'] >= args.intensity_thresh].copy()
    print(f"[INFO] After intensity filtering (≥{args.intensity_thresh}): {len(df):,} points")

    # 3) RANSAC ground-plane fit (Z = aX + bY + c)
    XY = df[['X','Y']].values
    Z  = df['Z'].values
    plane_model = make_pipeline(
        PolynomialFeatures(degree=1, include_bias=True),
        RANSACRegressor(
            estimator=LinearRegression(),
            residual_threshold=args.ground_threshold,
            random_state=0
        )
    )
    plane_model.fit(XY, Z)
    Z_pred = plane_model.predict(XY)

    # Keep only "ground" points
    df['residual'] = np.abs(Z - Z_pred)
    df = df[df['residual'] <= args.ground_threshold].copy()
    print(f"[INFO] After ground filtering (≤{args.ground_threshold}m): {len(df):,} points")

    # 4) DBSCAN clustering on XY
    coords = df[['X','Y']].values
    db     = DBSCAN(eps=args.eps, min_samples=args.min_samples).fit(coords)
    df['cluster'] = db.labels_

    # 5) Extract initial centerlines
    initial_lines = []
    for cid in sorted(df['cluster'].unique()):
        if cid < 0:
            continue
        pts  = df[df['cluster']==cid][['X','Y']].values
        hull = MultiPoint(pts).convex_hull

        # PCA → principal direction
        pca  = PCA(n_components=2).fit(pts)
        ctr  = pca.mean_
        dir0 = pca.components_[0]

        # Extend a long line along dir0 through centroid
        span = max(hull.bounds[2]-hull.bounds[0],
                   hull.bounds[3]-hull.bounds[1]) * 2.0
        L = LineString([
            tuple(ctr - dir0*span),
            tuple(ctr + dir0*span)
        ])

        # Intersect hull → chord(s)
        chord = hull.intersection(L)
        if chord.geom_type == 'MultiLineString':
            chord = max(chord.geoms, key=lambda seg: seg.length)
        if chord.geom_type != 'LineString':
            continue

        # Store line data
        line_data = type('LineData', (), {
            'geometry': chord,
            'properties': {
                'cluster': cid,
                'length': chord.length,
                'point_count': len(pts)
            }
        })()
        initial_lines.append(line_data)

    print(f"[INFO] Extracted {len(initial_lines)} initial centerline segments")

    # 6) Connect nearby lines
    connected_lines = connect_nearby_lines(initial_lines, args.max_gap, args.angle_threshold, args.lateral_threshold)

    # 7) Create output data
    ids = list(range(len(connected_lines)))
    geometries = [line.geometry for line in connected_lines]
    
    # Create properties for each connected line
    properties_list = []
    for i, line in enumerate(connected_lines):
        props = line.properties.copy()
        props['line_id'] = i
        properties_list.append(props)

    # 8) Wrap into GeoDataFrames
    header_crs = las.header.parse_crs()
    if header_crs is None:
        raise RuntimeError("LAS header has no CRS—please hardcode EPSG.")
    src_epsg = header_crs.to_epsg() or header_crs.to_string()

    gdf_pts  = gpd.GeoDataFrame(df, geometry=gpd.points_from_xy(df.X, df.Y), crs=src_epsg)
    gdf_line = gpd.GeoDataFrame(properties_list, geometry=geometries, crs=src_epsg)

    # 9) Reproject to target CRS & save GeoJSON
    gdf_pts  = gdf_pts .to_crs(args.target_crs)
    gdf_line = gdf_line.to_crs(args.target_crs)

    gdf_pts .to_file(f"{args.output_prefix}_points.geojson",      driver="GeoJSON")
    gdf_line.to_file(f"{args.output_prefix}_connected_lines.geojson", driver="GeoJSON")

    # 10) Create visualization
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 8))
    
    # Plot original fragments
    ax1.scatter(df.X, df.Y, s=1, c='lightgray', alpha=0.5)
    for line in initial_lines:
        xs, ys = line.geometry.xy
        ax1.plot(xs, ys, linewidth=1, alpha=0.7)
    ax1.set_title(f"Original Fragments ({len(initial_lines)} lines)")
    ax1.set_xlabel("X (m)")
    ax1.set_ylabel("Y (m)")
    ax1.axis('equal')
    
    # Plot connected lines
    ax2.scatter(df.X, df.Y, s=1, c='lightgray', alpha=0.5)
    for line in connected_lines:
        xs, ys = line.geometry.xy
        ax2.plot(xs, ys, linewidth=2, alpha=0.8)
    ax2.set_title(f"Connected Lines ({len(connected_lines)} lines)")
    ax2.set_xlabel("X (m)")
    ax2.set_ylabel("Y (m)")
    ax2.axis('equal')
    
    plt.tight_layout()
    plt.savefig(f"{args.output_prefix}_comparison.png", dpi=300, bbox_inches='tight')
    plt.close()

    print("→ Saved:")
    print(f"   • {args.output_prefix}_points.geojson")
    print(f"   • {args.output_prefix}_connected_lines.geojson")
    print(f"   • {args.output_prefix}_comparison.png")
    print(f"[SUCCESS] Connected {len(initial_lines)} fragments into {len(connected_lines)} continuous lines")

if __name__=="__main__":
    main()
