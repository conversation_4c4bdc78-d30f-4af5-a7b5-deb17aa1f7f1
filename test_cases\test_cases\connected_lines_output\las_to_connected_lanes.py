#!/usr/bin/env python3
import argparse
import laspy
import pandas as pd
import numpy as np
from sklearn.cluster import DBSCAN
from sklearn.decomposition import PCA
from sklearn.linear_model import RANSACRegressor, LinearRegression
from sklearn.preprocessing import PolynomialFeatures
from sklearn.pipeline import make_pipeline
from shapely.geometry import MultiPoint, LineString, Point, Polygon
from shapely.ops import linemerge, unary_union
import geopandas as gpd
import matplotlib.pyplot as plt
from scipy.spatial.distance import cdist
import requests
import json
from pathlib import Path

def connect_dashed_lanes(lines, max_gap=15.0, angle_threshold=20.0, lateral_threshold=2.5):
    """Connect dashed lane segments that are aligned and form continuous lanes"""
    print(f"[INFO] Connecting dashed lanes (max_gap: {max_gap}m, angle_threshold: {angle_threshold}deg, lateral_threshold: {lateral_threshold}m)")

    if len(lines) < 2:
        return lines

    connected_lines = []
    used_indices = set()

    # Sort lines by length to prioritize longer segments
    sorted_lines = sorted(enumerate(lines), key=lambda x: x[1].geometry.length, reverse=True)

    for orig_idx, line1 in sorted_lines:
        if orig_idx in used_indices:
            continue

        # Start a new connected line group
        line_group = [line1]
        used_indices.add(orig_idx)

        # Find all lines that should be connected to this group
        changed = True
        while changed:
            changed = False

            for j, line2 in enumerate(lines):
                if j in used_indices:
                    continue

                # Check if line2 should be connected to any line in the group
                for group_line in line_group:
                    if should_connect_dashed_lines(group_line.geometry, line2.geometry, max_gap, angle_threshold, lateral_threshold):
                        line_group.append(line2)
                        used_indices.add(j)
                        changed = True
                        break

        # Merge the line group into a single line
        if len(line_group) > 1:
            merged_line = merge_dashed_line_group(line_group)
            if merged_line:
                connected_lines.append(merged_line)
        else:
            connected_lines.append(line_group[0])

    print(f"[INFO] Connected {len(lines)} fragments into {len(connected_lines)} dashed lanes")
    return connected_lines

def should_connect_dashed_lines(line1, line2, max_gap, angle_threshold, lateral_threshold):
    """Check if two lines should be connected as part of a dashed lane"""
    # Get endpoints
    line1_start, line1_end = Point(line1.coords[0]), Point(line1.coords[-1])
    line2_start, line2_end = Point(line2.coords[0]), Point(line2.coords[-1])

    # Calculate all possible endpoint distances
    distances = [
        line1_start.distance(line2_start),
        line1_start.distance(line2_end),
        line1_end.distance(line2_start),
        line1_end.distance(line2_end)
    ]

    min_distance = min(distances)

    # More lenient distance threshold for dashed lanes
    if min_distance > max_gap:
        return False

    # Check angle similarity
    angle1 = get_line_angle(line1)
    angle2 = get_line_angle(line2)
    angle_diff = abs(angle1 - angle2)
    angle_diff = min(angle_diff, 180 - angle_diff)  # Handle wraparound

    if angle_diff > angle_threshold:
        return False

    # Check if lines are collinear (for dashed lanes)
    if is_collinear(line1, line2, lateral_threshold):
        return True

    return False

def is_collinear(line1, line2, threshold):
    """Check if two lines are approximately collinear"""
    # Get line centers
    center1 = Point((line1.coords[0][0] + line1.coords[-1][0])/2,
                    (line1.coords[0][1] + line1.coords[-1][1])/2)
    center2 = Point((line2.coords[0][0] + line2.coords[-1][0])/2,
                    (line2.coords[0][1] + line2.coords[-1][1])/2)

    # Calculate distance from center2 to line1
    dist_to_line = line1.distance(center2)

    return dist_to_line <= threshold

def merge_dashed_line_group(line_group):
    """Merge a group of dashed line segments into a continuous line"""
    try:
        # Extract all coordinates from all lines
        all_coords = []
        for line_data in line_group:
            coords = list(line_data.geometry.coords)
            all_coords.extend(coords)

        if len(all_coords) < 2:
            return line_group[0]

        # Remove duplicate coordinates
        unique_coords = []
        for coord in all_coords:
            if not unique_coords or Point(coord).distance(Point(unique_coords[-1])) > 0.1:
                unique_coords.append(coord)

        if len(unique_coords) < 2:
            return line_group[0]

        # Sort coordinates along the line direction
        # Use PCA to find the main direction
        coords_array = np.array(unique_coords)
        pca = PCA(n_components=2).fit(coords_array)
        main_direction = pca.components_[0]

        # Project coordinates onto main direction
        projections = np.dot(coords_array, main_direction)
        sorted_indices = np.argsort(projections)
        sorted_coords = [unique_coords[i] for i in sorted_indices]

        # Create merged line
        merged_geometry = LineString(sorted_coords)

        # Combine properties
        total_point_count = sum(line.properties.get('point_count', 0) for line in line_group)
        merged_clusters = [line.properties.get('cluster', 0) for line in line_group]

        properties = {
            'cluster': merged_clusters[0],  # Use first cluster ID
            'length': merged_geometry.length,
            'point_count': total_point_count,
            'merged_from': float(len(line_group)),
            'line_id': line_group[0].properties.get('line_id', 0),
            'line_type': 'dashed_lane'
        }

        return type('LineData', (), {
            'geometry': merged_geometry,
            'properties': properties
        })()

    except Exception as e:
        print(f"[WARNING] Failed to merge dashed line group: {e}")
        return line_group[0]  # Return first line as fallback

def get_osm_data(bounds, tags=None):
    """Query OSM data for the given bounds"""
    if tags is None:
        # More lenient query - just get highways and let the enrichment handle attributes
        tags = {
            "highway": True
        }

    # Create Overpass query
    overpass_url = "http://overpass-api.de/api/interpreter"

    # Simplified query - just get highways in the area
    overpass_query = f"""
    [out:json][timeout:30];
    (
      way["highway"]({bounds['south']},{bounds['west']},{bounds['north']},{bounds['east']});
    );
    out geom;
    """

    try:
        print(f"[INFO] Querying OSM data for bounds: {bounds}")
        response = requests.get(overpass_url, params={'data': overpass_query}, timeout=30)
        response.raise_for_status()

        osm_data = response.json()
        print(f"[INFO] Retrieved {len(osm_data.get('elements', []))} OSM elements")

        return osm_data

    except Exception as e:
        print(f"[WARNING] Failed to query OSM data: {e}")
        return {"elements": []}

def osm_to_geodataframe(osm_data, crs="EPSG:4326"):
    """Convert OSM data to GeoDataFrame"""
    features = []

    for element in osm_data.get('elements', []):
        if element['type'] == 'way' and 'geometry' in element:
            # Extract coordinates
            coords = [(node['lon'], node['lat']) for node in element['geometry']]

            if len(coords) >= 2:
                # Create LineString
                geometry = LineString(coords)

                # Extract tags
                tags = element.get('tags', {})

                # Create feature
                feature = {
                    'osm_id': element['id'],
                    'osm_type': element['type'],
                    'geometry': geometry,
                    **tags  # Add all OSM tags as properties
                }
                features.append(feature)

    if features:
        gdf = gpd.GeoDataFrame(features, crs=crs)
        print(f"[INFO] Created GeoDataFrame with {len(gdf)} OSM features")
        return gdf
    else:
        print("[WARNING] No valid OSM features found")
        return gpd.GeoDataFrame(geometry=[], crs=crs)

def enrich_lines_with_osm(lidar_lines_gdf, osm_gdf, max_distance=50.0):
    """Enrich LiDAR lines with OSM attributes"""
    print(f"[INFO] Enriching {len(lidar_lines_gdf)} LiDAR lines with OSM data")

    if len(osm_gdf) == 0:
        print("[WARNING] No OSM data available for enrichment")
        return lidar_lines_gdf

    # Ensure both GDFs are in the same CRS
    if lidar_lines_gdf.crs != osm_gdf.crs:
        osm_gdf = osm_gdf.to_crs(lidar_lines_gdf.crs)

    enriched_lines = lidar_lines_gdf.copy()

    # Initialize OSM attribute columns
    osm_columns = ['osm_highway', 'osm_lanes', 'osm_maxspeed', 'osm_surface',
                   'osm_oneway', 'osm_width', 'osm_distance', 'osm_id']

    for col in osm_columns:
        enriched_lines[col] = None

    # For each LiDAR line, find the closest OSM feature
    for idx, lidar_line in enriched_lines.iterrows():
        min_distance = float('inf')
        closest_osm = None

        for osm_idx, osm_feature in osm_gdf.iterrows():
            distance = lidar_line.geometry.distance(osm_feature.geometry)

            if distance < min_distance and distance <= max_distance:
                min_distance = distance
                closest_osm = osm_feature

        # If we found a close OSM feature, add its attributes
        if closest_osm is not None:
            enriched_lines.at[idx, 'osm_highway'] = closest_osm.get('highway', None)
            enriched_lines.at[idx, 'osm_lanes'] = closest_osm.get('lanes', None)
            enriched_lines.at[idx, 'osm_maxspeed'] = closest_osm.get('maxspeed', None)
            enriched_lines.at[idx, 'osm_surface'] = closest_osm.get('surface', None)
            enriched_lines.at[idx, 'osm_oneway'] = closest_osm.get('oneway', None)
            enriched_lines.at[idx, 'osm_width'] = closest_osm.get('width', None)
            enriched_lines.at[idx, 'osm_distance'] = min_distance
            enriched_lines.at[idx, 'osm_id'] = closest_osm.get('osm_id', None)

    enriched_count = len(enriched_lines[enriched_lines['osm_id'].notna()])
    print(f"[INFO] Enriched {enriched_count}/{len(enriched_lines)} lines with OSM data")

    return enriched_lines

def use_existing_osm_enricher(lines_gdf, output_prefix, script_dir=None):
    """Use the existing OSM attribute enricher from HT412 pipeline"""
    try:
        # Save the lines as a temporary model_lines_only.geojson file
        temp_dir = Path("temp_osm_enrichment")
        temp_dir.mkdir(exist_ok=True)

        temp_model_file = temp_dir / "model_lines_only.geojson"
        lines_gdf.to_file(temp_model_file, driver="GeoJSON")

        # Import and use the existing OSM enricher
        if script_dir:
            osm_enricher_path = script_dir / "osm_attribute_enricher.py"
        else:
            # Try to find the OSM enricher in the HT412 directory
            current_dir = Path(__file__).parent
            osm_enricher_path = current_dir.parent / "ht412_model_lines_osm/osm_attribute_enricher.py"

        if not osm_enricher_path.exists():
            print(f"[WARNING] OSM enricher not found at: {osm_enricher_path}")
            return lines_gdf, gpd.GeoDataFrame(geometry=[], crs=lines_gdf.crs)

        # Import the OSM enricher class
        import sys
        sys.path.insert(0, str(osm_enricher_path.parent))

        from osm_attribute_enricher import OSMAttributeEnricher

        # Create enricher instance
        enricher = OSMAttributeEnricher(temp_dir)

        # Load the model lines
        enricher.load_model_lines()

        # Get study area bounds and query OSM
        utm_bounds = enricher.get_study_area_bounds()
        wgs84_bounds = enricher.transform_bounds_to_wgs84(utm_bounds)
        osm_gdf = enricher.query_osm_data(wgs84_bounds)

        # Associate OSM attributes with lines
        enriched_lines = enricher.associate_osm_attributes(osm_gdf)

        # Save OSM data
        osm_output_file = f"{output_prefix}_osm_data.geojson"
        if len(osm_gdf) > 0:
            osm_gdf.to_file(osm_output_file, driver="GeoJSON")
            print(f"[INFO] Saved OSM data: {osm_output_file}")

        # Clean up temp directory
        import shutil
        shutil.rmtree(temp_dir, ignore_errors=True)

        print(f"[INFO] OSM enrichment completed using existing enricher")
        return enriched_lines, osm_gdf

    except Exception as e:
        print(f"[WARNING] Failed to use existing OSM enricher: {e}")
        print("[INFO] Falling back to basic OSM integration")
        return lines_gdf, gpd.GeoDataFrame(geometry=[], crs=lines_gdf.crs)

def connect_nearby_lines(lines, max_gap=5.0, angle_threshold=30.0, lateral_threshold=3.0):
    """Connect nearby line segments that are likely part of the same lane"""
    print(f"[INFO] Connecting nearby lines (max_gap: {max_gap}m, angle_threshold: {angle_threshold}deg, lateral_threshold: {lateral_threshold}m)")

    if len(lines) < 2:
        return lines

    connected_lines = []
    used_indices = set()

    for i, line1 in enumerate(lines):
        if i in used_indices:
            continue

        # Start a new connected line group
        line_group = [line1]
        used_indices.add(i)

        # Find all lines that should be connected to this group
        changed = True
        while changed:
            changed = False

            for j, line2 in enumerate(lines):
                if j in used_indices:
                    continue

                # Check if line2 should be connected to any line in the group
                for group_line in line_group:
                    if should_connect_lines(group_line.geometry, line2.geometry, max_gap, angle_threshold, lateral_threshold):
                        line_group.append(line2)
                        used_indices.add(j)
                        changed = True
                        break

        # Merge the line group into a single line
        if len(line_group) > 1:
            merged_line = merge_line_group(line_group)
            if merged_line:
                connected_lines.append(merged_line)
        else:
            connected_lines.append(line_group[0])

    print(f"[INFO] Connected {len(lines)} fragments into {len(connected_lines)} continuous lines")
    return connected_lines

def should_connect_lines(line1, line2, max_gap, angle_threshold, lateral_threshold):
    """Check if two lines should be connected based on proximity, angle, and lateral distance"""
    # Get endpoints
    line1_start, line1_end = Point(line1.coords[0]), Point(line1.coords[-1])
    line2_start, line2_end = Point(line2.coords[0]), Point(line2.coords[-1])

    # Calculate all possible endpoint distances
    distances = [
        line1_start.distance(line2_start),
        line1_start.distance(line2_end),
        line1_end.distance(line2_start),
        line1_end.distance(line2_end)
    ]

    min_distance = min(distances)

    # Check distance threshold
    if min_distance > max_gap:
        return False

    # Check angle similarity
    angle1 = get_line_angle(line1)
    angle2 = get_line_angle(line2)
    angle_diff = abs(angle1 - angle2)
    angle_diff = min(angle_diff, 180 - angle_diff)  # Handle wraparound

    if angle_diff > angle_threshold:
        return False

    # Check lateral distance (perpendicular distance between line centers)
    # Get line centers
    center1 = Point((line1.coords[0][0] + line1.coords[-1][0])/2,
                    (line1.coords[0][1] + line1.coords[-1][1])/2)
    center2 = Point((line2.coords[0][0] + line2.coords[-1][0])/2,
                    (line2.coords[0][1] + line2.coords[-1][1])/2)

    # Calculate perpendicular distance
    # Use the average angle for perpendicular calculation
    avg_angle = (angle1 + angle2) / 2
    perp_angle = avg_angle + 90

    # Vector between centers
    dx = center2.x - center1.x
    dy = center2.y - center1.y

    # Project onto perpendicular direction
    perp_rad = np.radians(perp_angle)
    perp_x = np.cos(perp_rad)
    perp_y = np.sin(perp_rad)

    lateral_dist = abs(dx * perp_x + dy * perp_y)

    return lateral_dist <= lateral_threshold

def get_line_angle(line):
    """Get the angle of a line in degrees"""
    coords = list(line.coords)
    if len(coords) < 2:
        return 0
    
    start, end = coords[0], coords[-1]
    dx = end[0] - start[0]
    dy = end[1] - start[1]
    
    angle = np.degrees(np.arctan2(dy, dx))
    return angle % 180  # Normalize to 0-180

def merge_line_group(line_group):
    """Merge a group of lines into a single continuous line"""
    try:
        # Extract all coordinates from all lines
        all_coords = []
        for line_data in line_group:
            coords = list(line_data.geometry.coords)
            all_coords.extend(coords)
        
        if len(all_coords) < 2:
            return None
        
        # Remove duplicate points
        unique_coords = []
        for coord in all_coords:
            if not unique_coords or Point(coord).distance(Point(unique_coords[-1])) > 0.1:
                unique_coords.append(coord)
        
        if len(unique_coords) < 2:
            return None
        
        # Fit a line through all points using PCA
        coords_array = np.array(unique_coords)
        pca = PCA(n_components=2).fit(coords_array)
        center = pca.mean_
        direction = pca.components_[0]
        
        # Project all points onto the principal axis
        projections = []
        for coord in unique_coords:
            point_vec = np.array(coord) - center
            projection = np.dot(point_vec, direction)
            projections.append(projection)
        
        # Find the extent of projections
        min_proj, max_proj = min(projections), max(projections)
        
        # Create the merged line
        start_point = center + direction * min_proj
        end_point = center + direction * max_proj
        
        merged_line = LineString([start_point, end_point])
        
        # Combine properties from the first line
        properties = line_group[0].properties.copy()
        properties['merged_from'] = len(line_group)
        properties['length'] = merged_line.length
        
        return type('LineData', (), {
            'geometry': merged_line,
            'properties': properties
        })()
        
    except Exception as e:
        print(f"[WARNING] Failed to merge line group: {e}")
        return line_group[0]  # Return first line as fallback

def main():
    p = argparse.ArgumentParser(
        description="LAS → Connected centerlines with line merging for continuous lanes"
    )
    p.add_argument('-i','--input', required=True, help="Input LAS file")
    p.add_argument('-t','--intensity-thresh', type=float, default=30.0,
                   help="Min intensity to keep a point")
    p.add_argument('--ground-threshold', type=float, default=0.05,
                   help="Max Z residual (m) to classify as ground after RANSAC")
    p.add_argument('--eps', type=float, default=0.8,
                   help="DBSCAN eps (metres)")
    p.add_argument('--min-samples', type=int, default=15,
                   help="DBSCAN min_samples")
    p.add_argument('--max-gap', type=float, default=3.0,
                   help="Maximum gap to connect lines (metres)")
    p.add_argument('--angle-threshold', type=float, default=15.0,
                   help="Maximum angle difference to connect lines (degrees)")
    p.add_argument('--lateral-threshold', type=float, default=2.0,
                   help="Maximum lateral distance to connect lines (metres)")

    # Dashed lane connection parameters
    p.add_argument('--connect-dashed', action='store_true',
                   help="Enable dashed lane connection with lenient parameters")
    p.add_argument('--dashed-gap', type=float, default=15.0,
                   help="Maximum gap for dashed lane connection (metres)")
    p.add_argument('--dashed-angle', type=float, default=20.0,
                   help="Maximum angle difference for dashed lanes (degrees)")
    p.add_argument('--dashed-lateral', type=float, default=2.5,
                   help="Maximum lateral distance for dashed lanes (metres)")

    # OSM integration parameters
    p.add_argument('--integrate-osm', action='store_true',
                   help="Enable OSM data integration")
    p.add_argument('--osm-distance', type=float, default=50.0,
                   help="Maximum distance to associate with OSM features (metres)")

    p.add_argument('-o','--output-prefix', default='connected_lanes',
                   help="Prefix for all output files")
    p.add_argument('--target-crs', default="EPSG:4326",
                   help="Output CRS (e.g. WGS84 lat/lon)")
    args = p.parse_args()

    # 1) Read LAS → xyz + intensity
    las   = laspy.read(args.input)
    xyz   = las.xyz                      # (N×3)
    inten = np.array(las.intensity, float)

    # 2) Filter by intensity
    df = pd.DataFrame(xyz, columns=['X','Y','Z'])
    df['Intensity'] = inten
    df = df[df['Intensity'] >= args.intensity_thresh].copy()
    print(f"[INFO] After intensity filtering (>={args.intensity_thresh}): {len(df):,} points")

    # 3) RANSAC ground-plane fit (Z = aX + bY + c)
    XY = df[['X','Y']].values
    Z  = df['Z'].values
    plane_model = make_pipeline(
        PolynomialFeatures(degree=1, include_bias=True),
        RANSACRegressor(
            estimator=LinearRegression(),
            residual_threshold=args.ground_threshold,
            random_state=0
        )
    )
    plane_model.fit(XY, Z)
    Z_pred = plane_model.predict(XY)

    # Keep only "ground" points
    df['residual'] = np.abs(Z - Z_pred)
    df = df[df['residual'] <= args.ground_threshold].copy()
    print(f"[INFO] After ground filtering (<={args.ground_threshold}m): {len(df):,} points")

    # 4) DBSCAN clustering on XY
    coords = df[['X','Y']].values
    db     = DBSCAN(eps=args.eps, min_samples=args.min_samples).fit(coords)
    df['cluster'] = db.labels_

    # 5) Extract initial centerlines
    initial_lines = []
    for cid in sorted(df['cluster'].unique()):
        if cid < 0:
            continue
        pts  = df[df['cluster']==cid][['X','Y']].values
        hull = MultiPoint(pts).convex_hull

        # PCA → principal direction
        pca  = PCA(n_components=2).fit(pts)
        ctr  = pca.mean_
        dir0 = pca.components_[0]

        # Extend a long line along dir0 through centroid
        span = max(hull.bounds[2]-hull.bounds[0],
                   hull.bounds[3]-hull.bounds[1]) * 2.0
        L = LineString([
            tuple(ctr - dir0*span),
            tuple(ctr + dir0*span)
        ])

        # Intersect hull → chord(s)
        chord = hull.intersection(L)
        if chord.geom_type == 'MultiLineString':
            chord = max(chord.geoms, key=lambda seg: seg.length)
        if chord.geom_type != 'LineString':
            continue

        # Store line data
        line_data = type('LineData', (), {
            'geometry': chord,
            'properties': {
                'cluster': cid,
                'length': chord.length,
                'point_count': len(pts)
            }
        })()
        initial_lines.append(line_data)

    print(f"[INFO] Extracted {len(initial_lines)} initial centerline segments")

    # 6) Connect nearby lines (regular connection)
    connected_lines = connect_nearby_lines(initial_lines, args.max_gap, args.angle_threshold, args.lateral_threshold)

    # 6b) Connect dashed lanes with more lenient parameters
    if args.connect_dashed:
        print("[INFO] Applying dashed lane connection...")
        connected_lines = connect_dashed_lanes(connected_lines,
                                             max_gap=args.dashed_gap,
                                             angle_threshold=args.dashed_angle,
                                             lateral_threshold=args.dashed_lateral)

    # 7) Create initial GeoDataFrame
    geometries = [line.geometry for line in connected_lines]
    
    # Create properties for each connected line
    properties_list = []
    for i, line in enumerate(connected_lines):
        props = line.properties.copy()
        props['line_id'] = i
        properties_list.append(props)

    # 8) Wrap into GeoDataFrames
    header_crs = las.header.parse_crs()
    if header_crs is None:
        raise RuntimeError("LAS header has no CRS—please hardcode EPSG.")
    src_epsg = header_crs.to_epsg() or header_crs.to_string()

    gdf_pts  = gpd.GeoDataFrame(df, geometry=gpd.points_from_xy(df.X, df.Y), crs=src_epsg)
    gdf_line = gpd.GeoDataFrame(properties_list, geometry=geometries, crs=src_epsg)

    # 9) Reproject to target CRS
    gdf_pts  = gdf_pts .to_crs(args.target_crs)
    gdf_line = gdf_line.to_crs(args.target_crs)

    # 9b) OSM Integration
    if args.integrate_osm:
        print("[INFO] Integrating with OSM data using existing HT412 enricher...")

        # Try to use the existing OSM enricher first
        script_dir = None
        current_dir = Path(__file__).parent
        ht412_dir = current_dir.parent / "ht412_model_lines_osm"

        if ht412_dir.exists():
            script_dir = ht412_dir
            print(f"[INFO] Found HT412 directory: {ht412_dir}")

        try:
            enriched_gdf, osm_gdf = use_existing_osm_enricher(gdf_line, args.output_prefix, script_dir)
            gdf_line = enriched_gdf

        except Exception as e:
            print(f"[WARNING] Failed to use existing OSM enricher: {e}")
            print("[INFO] Falling back to basic OSM integration...")

            # Fallback to basic OSM integration
            # Get bounds for OSM query - ensure they are in WGS84
            if args.target_crs != "EPSG:4326":
                # Convert bounds to WGS84 for OSM query
                gdf_line_wgs84 = gdf_line.to_crs("EPSG:4326")
                bounds = gdf_line_wgs84.total_bounds
            else:
                bounds = gdf_line.total_bounds

            osm_bounds = {
                'west': bounds[0],
                'south': bounds[1],
                'east': bounds[2],
                'north': bounds[3]
            }

            print(f"[INFO] OSM query bounds (WGS84): {osm_bounds}")

            # Query OSM data
            osm_data = get_osm_data(osm_bounds)
            osm_gdf = osm_to_geodataframe(osm_data, crs="EPSG:4326")

            # Convert OSM data to target CRS if needed
            if args.target_crs != "EPSG:4326":
                osm_gdf = osm_gdf.to_crs(args.target_crs)

            # Enrich lines with OSM attributes
            if len(osm_gdf) > 0:
                gdf_line = enrich_lines_with_osm(gdf_line, osm_gdf, max_distance=args.osm_distance)

                # Save OSM data separately
                osm_gdf.to_file(f"{args.output_prefix}_osm_data.geojson", driver="GeoJSON")
                print(f"[INFO] Saved OSM data: {args.output_prefix}_osm_data.geojson")

    # 10) Save GeoJSON files
    gdf_pts .to_file(f"{args.output_prefix}_points.geojson",      driver="GeoJSON")
    gdf_line.to_file(f"{args.output_prefix}_connected_lines.geojson", driver="GeoJSON")

    # Save enriched version if OSM integration was used
    if args.integrate_osm and 'osm_id' in gdf_line.columns:
        enriched_gdf = gdf_line[gdf_line['osm_id'].notna()].copy()
        if len(enriched_gdf) > 0:
            enriched_gdf.to_file(f"{args.output_prefix}_enriched_lines.geojson", driver="GeoJSON")
            print(f"[INFO] Saved enriched lines: {args.output_prefix}_enriched_lines.geojson")

    # 10) Create visualization
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 8))
    
    # Plot original fragments
    ax1.scatter(df.X, df.Y, s=1, c='lightgray', alpha=0.5)
    for line in initial_lines:
        xs, ys = line.geometry.xy
        ax1.plot(xs, ys, linewidth=1, alpha=0.7)
    ax1.set_title(f"Original Fragments ({len(initial_lines)} lines)")
    ax1.set_xlabel("X (m)")
    ax1.set_ylabel("Y (m)")
    ax1.axis('equal')
    
    # Plot connected lines
    ax2.scatter(df.X, df.Y, s=1, c='lightgray', alpha=0.5)
    for line in connected_lines:
        xs, ys = line.geometry.xy
        ax2.plot(xs, ys, linewidth=2, alpha=0.8)
    ax2.set_title(f"Connected Lines ({len(connected_lines)} lines)")
    ax2.set_xlabel("X (m)")
    ax2.set_ylabel("Y (m)")
    ax2.axis('equal')
    
    plt.tight_layout()
    plt.savefig(f"{args.output_prefix}_comparison.png", dpi=300, bbox_inches='tight')
    plt.close()

    print("-> Saved:")
    print(f"   • {args.output_prefix}_points.geojson")
    print(f"   • {args.output_prefix}_connected_lines.geojson")
    if args.integrate_osm:
        print(f"   • {args.output_prefix}_osm_data.geojson")
        if 'osm_id' in gdf_line.columns and len(gdf_line[gdf_line['osm_id'].notna()]) > 0:
            print(f"   • {args.output_prefix}_enriched_lines.geojson")
    print(f"   • {args.output_prefix}_comparison.png")

    connection_msg = f"Connected {len(initial_lines)} fragments into {len(connected_lines)} continuous lines"
    if args.connect_dashed:
        connection_msg += " (including dashed lane connection)"
    if args.integrate_osm:
        enriched_count = len(gdf_line[gdf_line['osm_id'].notna()]) if 'osm_id' in gdf_line.columns else 0
        connection_msg += f" with {enriched_count} OSM-enriched features"

    print(f"[SUCCESS] {connection_msg}")

if __name__=="__main__":
    main()
