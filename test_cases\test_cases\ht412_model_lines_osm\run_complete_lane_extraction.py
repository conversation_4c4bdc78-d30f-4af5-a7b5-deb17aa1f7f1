#!/usr/bin/env python3
"""
HT412 Complete Lane Extraction Runner
Simple script to run the complete lane extraction pipeline with HT412-optimized settings
"""

import subprocess
import sys
from pathlib import Path

def main():
    """Run the complete lane extraction pipeline for HT412 dataset"""
    
    print("=" * 60)
    print("HT412 COMPLETE LANE EXTRACTION")
    print("=" * 60)
    print("This script runs the enhanced lane extraction pipeline")
    print("with dashed lane connection and OSM integration.")
    print()
    
    # Check if the HT412 LAS file exists
    las_file = "D:/Integrate/HT412_1738935654_3217135_1422974157258029_1422974185645127_Clip.las"
    
    if not Path(las_file).exists():
        print(f"❌ ERROR: HT412 LAS file not found at: {las_file}")
        print("Please ensure the file exists at the specified location.")
        return 1
    
    print(f"✓ Found HT412 LAS file: {Path(las_file).name}")
    print()
    
    # Run different configurations
    configurations = [
        {
            "name": "Standard HT412 Configuration",
            "description": "Optimized for HT412 intensity range 20-45",
            "args": [
                "--intensity-thresh", "22",
                "--dashed-gap", "20",
                "--dashed-angle", "25",
                "-o", "ht412_standard_lanes"
            ]
        },
        {
            "name": "High Quality Configuration", 
            "description": "Enhanced settings for maximum lane detection",
            "args": [
                "--intensity-thresh", "20",
                "--dashed-gap", "25", 
                "--dashed-angle", "30",
                "--osm-distance", "100",
                "-o", "ht412_high_quality_lanes"
            ]
        },
        {
            "name": "UTM32N Configuration",
            "description": "Output in UTM32N to match existing pipeline",
            "args": [
                "--intensity-thresh", "22",
                "--target-crs", "EPSG:25832",
                "-o", "ht412_utm32n_lanes"
            ]
        }
    ]
    
    print("Available configurations:")
    for i, config in enumerate(configurations, 1):
        print(f"  {i}. {config['name']}")
        print(f"     {config['description']}")
    print()
    
    # Ask user which configuration to run
    try:
        choice = input("Select configuration (1-3) or press Enter for standard: ").strip()
        if not choice:
            choice = "1"
        
        config_index = int(choice) - 1
        if config_index < 0 or config_index >= len(configurations):
            print("Invalid choice, using standard configuration.")
            config_index = 0
            
    except (ValueError, KeyboardInterrupt):
        print("Using standard configuration.")
        config_index = 0
    
    selected_config = configurations[config_index]
    print(f"\n🚀 Running: {selected_config['name']}")
    print(f"   {selected_config['description']}")
    print()
    
    # Build and run command
    script_path = Path(__file__).parent / "complete_lane_extraction_pipeline.py"
    
    cmd = [sys.executable, str(script_path)] + selected_config["args"]
    
    print("Command:")
    print(" ".join(cmd))
    print()
    
    try:
        # Run the pipeline
        result = subprocess.run(cmd, check=True)
        
        print("\n" + "=" * 60)
        print("✅ PIPELINE COMPLETED SUCCESSFULLY!")
        print("=" * 60)
        
        # Show output files
        output_prefix = None
        for i, arg in enumerate(selected_config["args"]):
            if arg == "-o" and i + 1 < len(selected_config["args"]):
                output_prefix = selected_config["args"][i + 1]
                break
        
        if output_prefix:
            print(f"\nGenerated files with prefix: {output_prefix}")
            print("Key outputs:")
            print(f"  • {output_prefix}_connected_lines.geojson - Main lane lines")
            print(f"  • {output_prefix}_comparison.png - Before/after visualization")
            print(f"  • {output_prefix}_osm_data.geojson - OSM road data")
            
            # Check if files exist
            main_file = Path(f"{output_prefix}_connected_lines.geojson")
            if main_file.exists():
                file_size = main_file.stat().st_size / (1024 * 1024)
                print(f"\n📊 Main output: {main_file.name} ({file_size:.2f} MB)")
        
        print(f"\n🎯 Next steps:")
        print(f"  1. Open the comparison PNG to see the improvement")
        print(f"  2. Load the GeoJSON files in QGIS for analysis")
        print(f"  3. Compare with existing model_lines_with_osm_final.geojson")
        
        return 0
        
    except subprocess.CalledProcessError as e:
        print(f"\n❌ PIPELINE FAILED with return code {e.returncode}")
        print("Check the error messages above for details.")
        return 1
    
    except KeyboardInterrupt:
        print(f"\n⏹️  Pipeline interrupted by user.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
