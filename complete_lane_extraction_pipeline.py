#!/usr/bin/env python3
"""
Complete Lane Extraction Pipeline
Combines LiDAR processing, dashed lane connection, and OSM integration
"""

import argparse
import subprocess
import sys
from pathlib import Path
import json
import pandas as pd

def run_complete_pipeline(las_file, output_prefix="complete_lanes", **kwargs):
    """Run the complete lane extraction pipeline"""
    
    print("=" * 60)
    print("COMPLETE LANE EXTRACTION PIPELINE")
    print("=" * 60)
    print(f"Input LAS file: {las_file}")
    print(f"Output prefix: {output_prefix}")
    print()
    
    # Build command
    script_path = Path(__file__).parent / "test_cases/test_cases/connected_lines_output/las_to_connected_lanes.py"
    
    cmd = [
        sys.executable, str(script_path),
        "-i", las_file,
        "-o", output_prefix,
        "--connect-dashed",
        "--integrate-osm"
    ]
    
    # Add optional parameters
    if kwargs.get('intensity_thresh'):
        cmd.extend(["--intensity-thresh", str(kwargs['intensity_thresh'])])
    if kwargs.get('max_gap'):
        cmd.extend(["--max-gap", str(kwargs['max_gap'])])
    if kwargs.get('angle_threshold'):
        cmd.extend(["--angle-threshold", str(kwargs['angle_threshold'])])
    if kwargs.get('lateral_threshold'):
        cmd.extend(["--lateral-threshold", str(kwargs['lateral_threshold'])])
    if kwargs.get('dashed_gap'):
        cmd.extend(["--dashed-gap", str(kwargs['dashed_gap'])])
    if kwargs.get('dashed_angle'):
        cmd.extend(["--dashed-angle", str(kwargs['dashed_angle'])])
    if kwargs.get('dashed_lateral'):
        cmd.extend(["--dashed-lateral", str(kwargs['dashed_lateral'])])
    if kwargs.get('osm_distance'):
        cmd.extend(["--osm-distance", str(kwargs['osm_distance'])])
    if kwargs.get('target_crs'):
        cmd.extend(["--target-crs", kwargs['target_crs']])
    
    print("Running command:")
    print(" ".join(cmd))
    print()
    
    # Run the pipeline
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, check=True)
        print("PIPELINE OUTPUT:")
        print("-" * 40)
        print(result.stdout)
        
        if result.stderr:
            print("WARNINGS/ERRORS:")
            print("-" * 40)
            print(result.stderr)
        
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"ERROR: Pipeline failed with return code {e.returncode}")
        print("STDOUT:", e.stdout)
        print("STDERR:", e.stderr)
        return False

def create_summary_report(output_prefix):
    """Create a summary report of the pipeline results"""
    
    print("\n" + "=" * 60)
    print("PIPELINE SUMMARY REPORT")
    print("=" * 60)
    
    # Check which files were created
    files_created = []
    expected_files = [
        f"{output_prefix}_points.geojson",
        f"{output_prefix}_connected_lines.geojson", 
        f"{output_prefix}_osm_data.geojson",
        f"{output_prefix}_enriched_lines.geojson",
        f"{output_prefix}_comparison.png"
    ]
    
    for file_path in expected_files:
        if Path(file_path).exists():
            file_size = Path(file_path).stat().st_size
            files_created.append((file_path, file_size))
    
    print("FILES CREATED:")
    print("-" * 40)
    for file_path, size in files_created:
        size_mb = size / (1024 * 1024)
        print(f"  ✓ {file_path} ({size_mb:.2f} MB)")
    
    # Try to read and summarize the main results
    main_file = f"{output_prefix}_connected_lines.geojson"
    if Path(main_file).exists():
        try:
            import geopandas as gpd
            gdf = gpd.read_file(main_file)
            
            print(f"\nLANE EXTRACTION RESULTS:")
            print("-" * 40)
            print(f"  Total lanes extracted: {len(gdf)}")
            print(f"  Total length: {gdf['length'].sum():.2f} meters")
            print(f"  Average lane length: {gdf['length'].mean():.2f} meters")
            
            # Count different line types
            if 'line_type' in gdf.columns:
                line_types = gdf['line_type'].value_counts()
                print(f"  Line types:")
                for line_type, count in line_types.items():
                    if pd.notna(line_type):
                        print(f"    - {line_type}: {count}")
                    else:
                        print(f"    - regular: {count}")
            
            # Count merged lines
            if 'merged_from' in gdf.columns:
                merged_lines = gdf[gdf['merged_from'].notna()]
                print(f"  Merged lines: {len(merged_lines)}")
            
            # OSM enrichment stats
            if 'osm_id' in gdf.columns:
                enriched_lines = gdf[gdf['osm_id'].notna()]
                print(f"  OSM-enriched lines: {len(enriched_lines)}")
                
                if len(enriched_lines) > 0:
                    print(f"  OSM highway types:")
                    highway_types = enriched_lines['osm_highway'].value_counts()
                    for highway_type, count in highway_types.items():
                        if pd.notna(highway_type):
                            print(f"    - {highway_type}: {count}")
            
        except Exception as e:
            print(f"  Could not analyze results: {e}")
    
    print(f"\nVISUALIZATION:")
    print("-" * 40)
    comparison_file = f"{output_prefix}_comparison.png"
    if Path(comparison_file).exists():
        print(f"  ✓ Comparison plot: {comparison_file}")
        print(f"    Open this file to see before/after visualization")
    
    print(f"\nRECOMMENDED NEXT STEPS:")
    print("-" * 40)
    print(f"  1. View the comparison plot: {comparison_file}")
    print(f"  2. Load the main results in QGIS: {output_prefix}_connected_lines.geojson")
    print(f"  3. Check OSM integration: {output_prefix}_osm_data.geojson")
    if Path(f"{output_prefix}_enriched_lines.geojson").exists():
        print(f"  4. Review enriched lines: {output_prefix}_enriched_lines.geojson")

def main():
    parser = argparse.ArgumentParser(
        description="Complete Lane Extraction Pipeline with Dashed Lane Connection and OSM Integration",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Basic usage
  python complete_lane_extraction_pipeline.py -i road_data.las
  
  # Custom parameters for better dashed lane connection
  python complete_lane_extraction_pipeline.py -i road_data.las \\
    --intensity-thresh 25 --dashed-gap 20 --dashed-angle 25
  
  # High-quality output with OSM integration
  python complete_lane_extraction_pipeline.py -i road_data.las \\
    -o high_quality_lanes --intensity-thresh 20 --osm-distance 75
        """
    )
    
    parser.add_argument('-i', '--input', required=True, help="Input LAS file")
    parser.add_argument('-o', '--output-prefix', default='complete_lanes', 
                       help="Output prefix for all files")
    
    # LiDAR processing parameters
    parser.add_argument('--intensity-thresh', type=float, default=25.0,
                       help="Minimum intensity threshold")
    parser.add_argument('--max-gap', type=float, default=3.0,
                       help="Maximum gap for regular line connection (meters)")
    parser.add_argument('--angle-threshold', type=float, default=15.0,
                       help="Maximum angle difference for regular connection (degrees)")
    parser.add_argument('--lateral-threshold', type=float, default=2.0,
                       help="Maximum lateral distance for regular connection (meters)")
    
    # Dashed lane parameters
    parser.add_argument('--dashed-gap', type=float, default=15.0,
                       help="Maximum gap for dashed lane connection (meters)")
    parser.add_argument('--dashed-angle', type=float, default=20.0,
                       help="Maximum angle difference for dashed lanes (degrees)")
    parser.add_argument('--dashed-lateral', type=float, default=2.5,
                       help="Maximum lateral distance for dashed lanes (meters)")
    
    # OSM parameters
    parser.add_argument('--osm-distance', type=float, default=50.0,
                       help="Maximum distance for OSM association (meters)")
    parser.add_argument('--target-crs', default="EPSG:4326",
                       help="Output coordinate system")
    
    args = parser.parse_args()
    
    # Check input file exists
    if not Path(args.input).exists():
        print(f"ERROR: Input file not found: {args.input}")
        return 1
    
    # Run the complete pipeline
    success = run_complete_pipeline(
        las_file=args.input,
        output_prefix=args.output_prefix,
        intensity_thresh=args.intensity_thresh,
        max_gap=args.max_gap,
        angle_threshold=args.angle_threshold,
        lateral_threshold=args.lateral_threshold,
        dashed_gap=args.dashed_gap,
        dashed_angle=args.dashed_angle,
        dashed_lateral=args.dashed_lateral,
        osm_distance=args.osm_distance,
        target_crs=args.target_crs
    )
    
    if success:
        create_summary_report(args.output_prefix)
        print(f"\n🎉 PIPELINE COMPLETED SUCCESSFULLY! 🎉")
        return 0
    else:
        print(f"\n❌ PIPELINE FAILED")
        return 1

if __name__ == "__main__":
    sys.exit(main())
