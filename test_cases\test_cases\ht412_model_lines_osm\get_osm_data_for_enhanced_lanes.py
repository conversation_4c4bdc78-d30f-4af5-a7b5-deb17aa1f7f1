#!/usr/bin/env python3
"""
Get OSM data for enhanced lanes and create enriched output
"""

import sys
from pathlib import Path
import geopandas as gpd
from osm_attribute_enricher import OSMAttributeEnricher

def main():
    """Get OSM data and enrich the enhanced lanes"""
    
    print("=" * 60)
    print("OSM DATA ENRICHMENT FOR ENHANCED LANES")
    print("=" * 60)
    
    # Check if enhanced lanes file exists
    enhanced_lanes_file = Path("ht412_enhanced_lanes_connected_lines.geojson")
    
    if not enhanced_lanes_file.exists():
        print(f"ERROR: Enhanced lanes file not found: {enhanced_lanes_file}")
        print("Please run the enhanced lane extraction first.")
        return 1
    
    print(f"Found enhanced lanes file: {enhanced_lanes_file}")
    
    # Load the enhanced lanes
    enhanced_lanes = gpd.read_file(enhanced_lanes_file)
    print(f"Loaded {len(enhanced_lanes)} enhanced lane lines")
    
    # Create temporary directory for OSM enricher
    temp_dir = Path("osm_enrichment_temp")
    temp_dir.mkdir(exist_ok=True)
    
    # Save enhanced lanes as model_lines_only.geojson for the enricher
    temp_model_file = temp_dir / "model_lines_only.geojson"
    enhanced_lanes.to_file(temp_model_file, driver="GeoJSON")
    
    try:
        # Create OSM enricher instance
        enricher = OSMAttributeEnricher(temp_dir)
        
        # Load the model lines
        enricher.load_model_lines()
        
        # Get study area bounds and transform to WGS84
        utm_bounds = enricher.get_study_area_bounds()
        wgs84_bounds = enricher.transform_bounds_to_wgs84(utm_bounds)
        
        # Query OSM data (returns list of features)
        osm_features = enricher.query_osm_data(wgs84_bounds)

        if len(osm_features) > 0:
            print(f"Successfully retrieved {len(osm_features)} OSM features")

            # Convert to GeoDataFrame
            osm_gdf = enricher.create_osm_geodataframe(osm_features)

            if len(osm_gdf) > 0:
                # Save raw OSM data
                osm_output_file = "ht412_enhanced_lanes_osm_data.geojson"
                osm_gdf.to_file(osm_output_file, driver="GeoJSON")
                print(f"Saved OSM data: {osm_output_file}")

                # Associate OSM attributes with enhanced lanes
                enriched_lanes = enricher.associate_osm_attributes(osm_gdf)

                # Save enriched lanes
                enriched_output_file = "ht412_enhanced_lanes_with_osm.geojson"
                enriched_lanes.to_file(enriched_output_file, driver="GeoJSON")
                print(f"Saved enriched lanes: {enriched_output_file}")

                # Print summary
                osm_enriched_count = len(enriched_lanes[enriched_lanes['osm_osm_id'].notna()])
                print(f"\nSUMMARY:")
                print(f"  Total enhanced lanes: {len(enriched_lanes)}")
                print(f"  OSM-enriched lanes: {osm_enriched_count}")
                print(f"  OSM features found: {len(osm_gdf)}")
            else:
                print("WARNING: No OSM GeoDataFrame created")
                return 1
            
            if osm_enriched_count > 0:
                print(f"\nOSM ATTRIBUTES FOUND:")
                osm_enriched = enriched_lanes[enriched_lanes['osm_osm_id'].notna()]
                
                if 'osm_highway' in osm_enriched.columns:
                    highway_types = osm_enriched['osm_highway'].value_counts()
                    print(f"  Highway types:")
                    for highway_type, count in highway_types.items():
                        if highway_type and str(highway_type) != 'nan':
                            print(f"    - {highway_type}: {count}")

                if 'osm_lanes' in osm_enriched.columns:
                    lanes_info = osm_enriched['osm_lanes'].value_counts()
                    print(f"  Lane counts:")
                    for lanes, count in lanes_info.items():
                        if lanes and str(lanes) != 'nan':
                            print(f"    - {lanes} lanes: {count}")

                if 'osm_maxspeed' in osm_enriched.columns:
                    speed_info = osm_enriched['osm_maxspeed'].value_counts()
                    print(f"  Speed limits:")
                    for speed, count in speed_info.items():
                        if speed and str(speed) != 'nan':
                            print(f"    - {speed}: {count}")

                if 'osm_ref' in osm_enriched.columns:
                    ref_info = osm_enriched['osm_ref'].value_counts()
                    print(f"  Road references:")
                    for ref, count in ref_info.items():
                        if ref and str(ref) != 'nan':
                            print(f"    - {ref}: {count}")
            
            print(f"\n✅ OSM enrichment completed successfully!")
            print(f"\nGenerated files:")
            print(f"  - {osm_output_file} - Raw OSM highway data")
            print(f"  - {enriched_output_file} - Enhanced lanes with OSM attributes")
            
        else:
            print("WARNING: No OSM features found in the area")
            return 1
            
    except Exception as e:
        print(f"ERROR: Failed to enrich with OSM data: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    finally:
        # Clean up temp directory
        import shutil
        shutil.rmtree(temp_dir, ignore_errors=True)
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
